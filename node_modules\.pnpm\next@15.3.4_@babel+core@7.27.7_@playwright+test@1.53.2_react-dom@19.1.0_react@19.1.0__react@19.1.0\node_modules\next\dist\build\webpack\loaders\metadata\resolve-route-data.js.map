{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/resolve-route-data.ts"], "sourcesContent": ["import type { MetadataRoute } from '../../../../lib/metadata/types/metadata-interface'\nimport { resolveArray } from '../../../../lib/metadata/generate/utils'\n\n// convert robots data to txt string\nexport function resolveRobots(data: MetadataRoute.Robots): string {\n  let content = ''\n  const rules = Array.isArray(data.rules) ? data.rules : [data.rules]\n  for (const rule of rules) {\n    const userAgent = resolveArray(rule.userAgent || ['*'])\n    for (const agent of userAgent) {\n      content += `User-Agent: ${agent}\\n`\n    }\n    if (rule.allow) {\n      const allow = resolveArray(rule.allow)\n      for (const item of allow) {\n        content += `Allow: ${item}\\n`\n      }\n    }\n    if (rule.disallow) {\n      const disallow = resolveArray(rule.disallow)\n      for (const item of disallow) {\n        content += `Disallow: ${item}\\n`\n      }\n    }\n    if (rule.crawlDelay) {\n      content += `Crawl-delay: ${rule.crawlDelay}\\n`\n    }\n    content += '\\n'\n  }\n  if (data.host) {\n    content += `Host: ${data.host}\\n`\n  }\n  if (data.sitemap) {\n    const sitemap = resolveArray(data.sitemap)\n    // TODO-METADATA: support injecting sitemap url into robots.txt\n    sitemap.forEach((item) => {\n      content += `Sitemap: ${item}\\n`\n    })\n  }\n\n  return content\n}\n\n// TODO-METADATA: support multi sitemap files\n// convert sitemap data to xml string\nexport function resolveSitemap(data: MetadataRoute.Sitemap): string {\n  const hasAlternates = data.some(\n    (item) => Object.keys(item.alternates ?? {}).length > 0\n  )\n  const hasImages = data.some((item) => Boolean(item.images?.length))\n  const hasVideos = data.some((item) => Boolean(item.videos?.length))\n\n  let content = ''\n  content += '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\\n'\n  content += '<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"'\n  if (hasImages) {\n    content += ' xmlns:image=\"http://www.google.com/schemas/sitemap-image/1.1\"'\n  }\n  if (hasVideos) {\n    content += ' xmlns:video=\"http://www.google.com/schemas/sitemap-video/1.1\"'\n  }\n  if (hasAlternates) {\n    content += ' xmlns:xhtml=\"http://www.w3.org/1999/xhtml\">\\n'\n  } else {\n    content += '>\\n'\n  }\n  for (const item of data) {\n    content += '<url>\\n'\n    content += `<loc>${item.url}</loc>\\n`\n\n    const languages = item.alternates?.languages\n    if (languages && Object.keys(languages).length) {\n      // Since sitemap is separated from the page rendering, there's not metadataBase accessible yet.\n      // we give the default setting that won't effect the languages resolving.\n      for (const language in languages) {\n        content += `<xhtml:link rel=\"alternate\" hreflang=\"${language}\" href=\"${\n          languages[language as keyof typeof languages]\n        }\" />\\n`\n      }\n    }\n    if (item.images?.length) {\n      for (const image of item.images) {\n        content += `<image:image>\\n<image:loc>${image}</image:loc>\\n</image:image>\\n`\n      }\n    }\n    if (item.videos?.length) {\n      for (const video of item.videos) {\n        let videoFields = [\n          `<video:video>`,\n          `<video:title>${video.title}</video:title>`,\n          `<video:thumbnail_loc>${video.thumbnail_loc}</video:thumbnail_loc>`,\n          `<video:description>${video.description}</video:description>`,\n          video.content_loc &&\n            `<video:content_loc>${video.content_loc}</video:content_loc>`,\n          video.player_loc &&\n            `<video:player_loc>${video.player_loc}</video:player_loc>`,\n          video.duration &&\n            `<video:duration>${video.duration}</video:duration>`,\n          video.view_count &&\n            `<video:view_count>${video.view_count}</video:view_count>`,\n          video.tag && `<video:tag>${video.tag}</video:tag>`,\n          video.rating && `<video:rating>${video.rating}</video:rating>`,\n          video.expiration_date &&\n            `<video:expiration_date>${video.expiration_date}</video:expiration_date>`,\n          video.publication_date &&\n            `<video:publication_date>${video.publication_date}</video:publication_date>`,\n          video.family_friendly &&\n            `<video:family_friendly>${video.family_friendly}</video:family_friendly>`,\n          video.requires_subscription &&\n            `<video:requires_subscription>${video.requires_subscription}</video:requires_subscription>`,\n          video.live && `<video:live>${video.live}</video:live>`,\n          video.restriction &&\n            `<video:restriction relationship=\"${video.restriction.relationship}\">${video.restriction.content}</video:restriction>`,\n          video.platform &&\n            `<video:platform relationship=\"${video.platform.relationship}\">${video.platform.content}</video:platform>`,\n          video.uploader &&\n            `<video:uploader${video.uploader.info && ` info=\"${video.uploader.info}\"`}>${video.uploader.content}</video:uploader>`,\n          `</video:video>\\n`,\n        ].filter(Boolean)\n        content += videoFields.join('\\n')\n      }\n    }\n    if (item.lastModified) {\n      const serializedDate =\n        item.lastModified instanceof Date\n          ? item.lastModified.toISOString()\n          : item.lastModified\n\n      content += `<lastmod>${serializedDate}</lastmod>\\n`\n    }\n\n    if (item.changeFrequency) {\n      content += `<changefreq>${item.changeFrequency}</changefreq>\\n`\n    }\n\n    if (typeof item.priority === 'number') {\n      content += `<priority>${item.priority}</priority>\\n`\n    }\n\n    content += '</url>\\n'\n  }\n\n  content += '</urlset>\\n'\n\n  return content\n}\n\nexport function resolveManifest(data: MetadataRoute.Manifest): string {\n  return JSON.stringify(data)\n}\n\nexport function resolveRouteData(\n  data: MetadataRoute.Robots | MetadataRoute.Sitemap | MetadataRoute.Manifest,\n  fileType: 'robots' | 'sitemap' | 'manifest'\n): string {\n  if (fileType === 'robots') {\n    return resolveRobots(data as MetadataRoute.Robots)\n  }\n  if (fileType === 'sitemap') {\n    return resolveSitemap(data as MetadataRoute.Sitemap)\n  }\n  if (fileType === 'manifest') {\n    return resolveManifest(data as MetadataRoute.Manifest)\n  }\n  return ''\n}\n"], "names": ["resolveManifest", "resolveRobots", "resolveRouteData", "resolveSitemap", "data", "content", "rules", "Array", "isArray", "rule", "userAgent", "resolveArray", "agent", "allow", "item", "disallow", "crawlDelay", "host", "sitemap", "for<PERSON>ach", "hasAlternates", "some", "Object", "keys", "alternates", "length", "hasImages", "Boolean", "images", "hasVideos", "videos", "url", "languages", "language", "image", "video", "videoFields", "title", "thumbnail_loc", "description", "content_loc", "player_loc", "duration", "view_count", "tag", "rating", "expiration_date", "publication_date", "family_friendly", "requires_subscription", "live", "restriction", "relationship", "platform", "uploader", "info", "filter", "join", "lastModified", "serializedDate", "Date", "toISOString", "changeFrequency", "priority", "JSON", "stringify", "fileType"], "mappings": ";;;;;;;;;;;;;;;;;IAmJgBA,eAAe;eAAfA;;IA/IAC,aAAa;eAAbA;;IAmJAC,gBAAgB;eAAhBA;;IA1GAC,cAAc;eAAdA;;;uBA5Ca;AAGtB,SAASF,cAAcG,IAA0B;IACtD,IAAIC,UAAU;IACd,MAAMC,QAAQC,MAAMC,OAAO,CAACJ,KAAKE,KAAK,IAAIF,KAAKE,KAAK,GAAG;QAACF,KAAKE,KAAK;KAAC;IACnE,KAAK,MAAMG,QAAQH,MAAO;QACxB,MAAMI,YAAYC,IAAAA,mBAAY,EAACF,KAAKC,SAAS,IAAI;YAAC;SAAI;QACtD,KAAK,MAAME,SAASF,UAAW;YAC7BL,WAAW,CAAC,YAAY,EAAEO,MAAM,EAAE,CAAC;QACrC;QACA,IAAIH,KAAKI,KAAK,EAAE;YACd,MAAMA,QAAQF,IAAAA,mBAAY,EAACF,KAAKI,KAAK;YACrC,KAAK,MAAMC,QAAQD,MAAO;gBACxBR,WAAW,CAAC,OAAO,EAAES,KAAK,EAAE,CAAC;YAC/B;QACF;QACA,IAAIL,KAAKM,QAAQ,EAAE;YACjB,MAAMA,WAAWJ,IAAAA,mBAAY,EAACF,KAAKM,QAAQ;YAC3C,KAAK,MAAMD,QAAQC,SAAU;gBAC3BV,WAAW,CAAC,UAAU,EAAES,KAAK,EAAE,CAAC;YAClC;QACF;QACA,IAAIL,KAAKO,UAAU,EAAE;YACnBX,WAAW,CAAC,aAAa,EAAEI,KAAKO,UAAU,CAAC,EAAE,CAAC;QAChD;QACAX,WAAW;IACb;IACA,IAAID,KAAKa,IAAI,EAAE;QACbZ,WAAW,CAAC,MAAM,EAAED,KAAKa,IAAI,CAAC,EAAE,CAAC;IACnC;IACA,IAAIb,KAAKc,OAAO,EAAE;QAChB,MAAMA,UAAUP,IAAAA,mBAAY,EAACP,KAAKc,OAAO;QACzC,+DAA+D;QAC/DA,QAAQC,OAAO,CAAC,CAACL;YACfT,WAAW,CAAC,SAAS,EAAES,KAAK,EAAE,CAAC;QACjC;IACF;IAEA,OAAOT;AACT;AAIO,SAASF,eAAeC,IAA2B;IACxD,MAAMgB,gBAAgBhB,KAAKiB,IAAI,CAC7B,CAACP,OAASQ,OAAOC,IAAI,CAACT,KAAKU,UAAU,IAAI,CAAC,GAAGC,MAAM,GAAG;IAExD,MAAMC,YAAYtB,KAAKiB,IAAI,CAAC,CAACP;YAAiBA;eAARa,SAAQb,eAAAA,KAAKc,MAAM,qBAAXd,aAAaW,MAAM;;IACjE,MAAMI,YAAYzB,KAAKiB,IAAI,CAAC,CAACP;YAAiBA;eAARa,SAAQb,eAAAA,KAAKgB,MAAM,qBAAXhB,aAAaW,MAAM;;IAEjE,IAAIpB,UAAU;IACdA,WAAW;IACXA,WAAW;IACX,IAAIqB,WAAW;QACbrB,WAAW;IACb;IACA,IAAIwB,WAAW;QACbxB,WAAW;IACb;IACA,IAAIe,eAAe;QACjBf,WAAW;IACb,OAAO;QACLA,WAAW;IACb;IACA,KAAK,MAAMS,QAAQV,KAAM;YAILU,kBAUdA,cAKAA;QAlBJT,WAAW;QACXA,WAAW,CAAC,KAAK,EAAES,KAAKiB,GAAG,CAAC,QAAQ,CAAC;QAErC,MAAMC,aAAYlB,mBAAAA,KAAKU,UAAU,qBAAfV,iBAAiBkB,SAAS;QAC5C,IAAIA,aAAaV,OAAOC,IAAI,CAACS,WAAWP,MAAM,EAAE;YAC9C,+FAA+F;YAC/F,yEAAyE;YACzE,IAAK,MAAMQ,YAAYD,UAAW;gBAChC3B,WAAW,CAAC,sCAAsC,EAAE4B,SAAS,QAAQ,EACnED,SAAS,CAACC,SAAmC,CAC9C,MAAM,CAAC;YACV;QACF;QACA,KAAInB,eAAAA,KAAKc,MAAM,qBAAXd,aAAaW,MAAM,EAAE;YACvB,KAAK,MAAMS,SAASpB,KAAKc,MAAM,CAAE;gBAC/BvB,WAAW,CAAC,0BAA0B,EAAE6B,MAAM,8BAA8B,CAAC;YAC/E;QACF;QACA,KAAIpB,eAAAA,KAAKgB,MAAM,qBAAXhB,aAAaW,MAAM,EAAE;YACvB,KAAK,MAAMU,SAASrB,KAAKgB,MAAM,CAAE;gBAC/B,IAAIM,cAAc;oBAChB,CAAC,aAAa,CAAC;oBACf,CAAC,aAAa,EAAED,MAAME,KAAK,CAAC,cAAc,CAAC;oBAC3C,CAAC,qBAAqB,EAAEF,MAAMG,aAAa,CAAC,sBAAsB,CAAC;oBACnE,CAAC,mBAAmB,EAAEH,MAAMI,WAAW,CAAC,oBAAoB,CAAC;oBAC7DJ,MAAMK,WAAW,IACf,CAAC,mBAAmB,EAAEL,MAAMK,WAAW,CAAC,oBAAoB,CAAC;oBAC/DL,MAAMM,UAAU,IACd,CAAC,kBAAkB,EAAEN,MAAMM,UAAU,CAAC,mBAAmB,CAAC;oBAC5DN,MAAMO,QAAQ,IACZ,CAAC,gBAAgB,EAAEP,MAAMO,QAAQ,CAAC,iBAAiB,CAAC;oBACtDP,MAAMQ,UAAU,IACd,CAAC,kBAAkB,EAAER,MAAMQ,UAAU,CAAC,mBAAmB,CAAC;oBAC5DR,MAAMS,GAAG,IAAI,CAAC,WAAW,EAAET,MAAMS,GAAG,CAAC,YAAY,CAAC;oBAClDT,MAAMU,MAAM,IAAI,CAAC,cAAc,EAAEV,MAAMU,MAAM,CAAC,eAAe,CAAC;oBAC9DV,MAAMW,eAAe,IACnB,CAAC,uBAAuB,EAAEX,MAAMW,eAAe,CAAC,wBAAwB,CAAC;oBAC3EX,MAAMY,gBAAgB,IACpB,CAAC,wBAAwB,EAAEZ,MAAMY,gBAAgB,CAAC,yBAAyB,CAAC;oBAC9EZ,MAAMa,eAAe,IACnB,CAAC,uBAAuB,EAAEb,MAAMa,eAAe,CAAC,wBAAwB,CAAC;oBAC3Eb,MAAMc,qBAAqB,IACzB,CAAC,6BAA6B,EAAEd,MAAMc,qBAAqB,CAAC,8BAA8B,CAAC;oBAC7Fd,MAAMe,IAAI,IAAI,CAAC,YAAY,EAAEf,MAAMe,IAAI,CAAC,aAAa,CAAC;oBACtDf,MAAMgB,WAAW,IACf,CAAC,iCAAiC,EAAEhB,MAAMgB,WAAW,CAACC,YAAY,CAAC,EAAE,EAAEjB,MAAMgB,WAAW,CAAC9C,OAAO,CAAC,oBAAoB,CAAC;oBACxH8B,MAAMkB,QAAQ,IACZ,CAAC,8BAA8B,EAAElB,MAAMkB,QAAQ,CAACD,YAAY,CAAC,EAAE,EAAEjB,MAAMkB,QAAQ,CAAChD,OAAO,CAAC,iBAAiB,CAAC;oBAC5G8B,MAAMmB,QAAQ,IACZ,CAAC,eAAe,EAAEnB,MAAMmB,QAAQ,CAACC,IAAI,IAAI,CAAC,OAAO,EAAEpB,MAAMmB,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEpB,MAAMmB,QAAQ,CAACjD,OAAO,CAAC,iBAAiB,CAAC;oBACxH,CAAC,gBAAgB,CAAC;iBACnB,CAACmD,MAAM,CAAC7B;gBACTtB,WAAW+B,YAAYqB,IAAI,CAAC;YAC9B;QACF;QACA,IAAI3C,KAAK4C,YAAY,EAAE;YACrB,MAAMC,iBACJ7C,KAAK4C,YAAY,YAAYE,OACzB9C,KAAK4C,YAAY,CAACG,WAAW,KAC7B/C,KAAK4C,YAAY;YAEvBrD,WAAW,CAAC,SAAS,EAAEsD,eAAe,YAAY,CAAC;QACrD;QAEA,IAAI7C,KAAKgD,eAAe,EAAE;YACxBzD,WAAW,CAAC,YAAY,EAAES,KAAKgD,eAAe,CAAC,eAAe,CAAC;QACjE;QAEA,IAAI,OAAOhD,KAAKiD,QAAQ,KAAK,UAAU;YACrC1D,WAAW,CAAC,UAAU,EAAES,KAAKiD,QAAQ,CAAC,aAAa,CAAC;QACtD;QAEA1D,WAAW;IACb;IAEAA,WAAW;IAEX,OAAOA;AACT;AAEO,SAASL,gBAAgBI,IAA4B;IAC1D,OAAO4D,KAAKC,SAAS,CAAC7D;AACxB;AAEO,SAASF,iBACdE,IAA2E,EAC3E8D,QAA2C;IAE3C,IAAIA,aAAa,UAAU;QACzB,OAAOjE,cAAcG;IACvB;IACA,IAAI8D,aAAa,WAAW;QAC1B,OAAO/D,eAAeC;IACxB;IACA,IAAI8D,aAAa,YAAY;QAC3B,OAAOlE,gBAAgBI;IACzB;IACA,OAAO;AACT"}