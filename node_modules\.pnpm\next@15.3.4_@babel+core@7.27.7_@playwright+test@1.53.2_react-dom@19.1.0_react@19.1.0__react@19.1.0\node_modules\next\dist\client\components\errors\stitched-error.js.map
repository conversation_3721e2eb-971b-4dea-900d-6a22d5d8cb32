{"version": 3, "sources": ["../../../../src/client/components/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../lib/is-error'\nimport { copyNextErrorCode } from '../../../lib/error-telemetry-utils'\n\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame'\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\n  `(at ${REACT_ERROR_STACK_BOTTOM_FRAME} )|(${REACT_ERROR_STACK_BOTTOM_FRAME}\\\\@)`\n)\n\nexport function getReactStitchedError<T = unknown>(err: T): Error | T {\n  const isErrorInstance = isError(err)\n  const originStack = isErrorInstance ? err.stack || '' : ''\n  const originMessage = isErrorInstance ? err.message : ''\n  const stackLines = originStack.split('\\n')\n  const indexOfSplit = stackLines.findIndex((line) =>\n    REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line)\n  )\n  const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n  let newStack = isOriginalReactError\n    ? stackLines.slice(0, indexOfSplit).join('\\n')\n    : originStack\n\n  const newError = new Error(originMessage)\n  // Copy all enumerable properties, e.g. digest\n  Object.assign(newError, err)\n  copyNextErrorCode(err, newError)\n  newError.stack = newStack\n\n  // Avoid duplicate overriding stack frames\n  appendOwnerStack(newError)\n\n  return newError\n}\n\nfunction appendOwnerStack(error: Error) {\n  if (!React.captureOwnerStack) {\n    return\n  }\n  let stack = error.stack || ''\n  // This module is only bundled in development mode so this is safe.\n  const ownerStack = React.captureOwnerStack()\n  // Avoid duplicate overriding stack frames\n  if (ownerStack && stack.endsWith(ownerStack) === false) {\n    stack += ownerStack\n    // Override stack\n    error.stack = stack\n  }\n}\n"], "names": ["getReactStitchedError", "REACT_ERROR_STACK_BOTTOM_FRAME", "REACT_ERROR_STACK_BOTTOM_FRAME_REGEX", "RegExp", "err", "isErrorInstance", "isError", "originStack", "stack", "originMessage", "message", "stackLines", "split", "indexOfSplit", "findIndex", "line", "test", "isOriginalReactError", "newStack", "slice", "join", "newError", "Error", "Object", "assign", "copyNextErrorCode", "appendOwnerStack", "error", "React", "captureOwnerStack", "ownerStack", "endsWith"], "mappings": ";;;;+BASgBA;;;eAAAA;;;;gEATE;kEACE;qCACc;AAElC,MAAMC,iCAAiC;AACvC,MAAMC,uCAAuC,IAAIC,OAC/C,AAAC,SAAMF,iCAA+B,SAAMA,iCAA+B;AAGtE,SAASD,sBAAmCI,GAAM;IACvD,MAAMC,kBAAkBC,IAAAA,gBAAO,EAACF;IAChC,MAAMG,cAAcF,kBAAkBD,IAAII,KAAK,IAAI,KAAK;IACxD,MAAMC,gBAAgBJ,kBAAkBD,IAAIM,OAAO,GAAG;IACtD,MAAMC,aAAaJ,YAAYK,KAAK,CAAC;IACrC,MAAMC,eAAeF,WAAWG,SAAS,CAAC,CAACC,OACzCb,qCAAqCc,IAAI,CAACD;IAE5C,MAAME,uBAAuBJ,gBAAgB,EAAE,mCAAmC;;IAClF,IAAIK,WAAWD,uBACXN,WAAWQ,KAAK,CAAC,GAAGN,cAAcO,IAAI,CAAC,QACvCb;IAEJ,MAAMc,WAAW,qBAAwB,CAAxB,IAAIC,MAAMb,gBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IACxC,8CAA8C;IAC9Cc,OAAOC,MAAM,CAACH,UAAUjB;IACxBqB,IAAAA,sCAAiB,EAACrB,KAAKiB;IACvBA,SAASb,KAAK,GAAGU;IAEjB,0CAA0C;IAC1CQ,iBAAiBL;IAEjB,OAAOA;AACT;AAEA,SAASK,iBAAiBC,KAAY;IACpC,IAAI,CAACC,cAAK,CAACC,iBAAiB,EAAE;QAC5B;IACF;IACA,IAAIrB,QAAQmB,MAAMnB,KAAK,IAAI;IAC3B,mEAAmE;IACnE,MAAMsB,aAAaF,cAAK,CAACC,iBAAiB;IAC1C,0CAA0C;IAC1C,IAAIC,cAActB,MAAMuB,QAAQ,CAACD,gBAAgB,OAAO;QACtDtB,SAASsB;QACT,iBAAiB;QACjBH,MAAMnB,KAAK,GAAGA;IAChB;AACF"}