{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/container/build-error.tsx"], "sourcesContent": ["import React, { useCallback, useMemo } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { Terminal } from '../components/terminal'\nimport { ErrorOverlayLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport type { ErrorBaseProps } from '../components/errors/error-overlay/error-overlay'\n\nexport interface BuildErrorProps extends ErrorBaseProps {\n  message: string\n}\n\nconst getErrorTextFromBuildErrorMessage = (multiLineMessage: string) => {\n  const lines = multiLineMessage.split('\\n')\n  // The multi-line build error message looks like:\n  // <file path>:<line number>:<column number>\n  // <error message>\n  // <error code frame of compiler or bundler>\n  // e.g.\n  // ./path/to/file.js:1:1\n  // SyntaxError: ...\n  // > 1 | con st foo =\n  // ...\n  return stripAnsi(lines[1] || '')\n}\n\nexport const BuildError: React.FC<BuildErrorProps> = function BuildError({\n  message,\n  ...props\n}) {\n  const noop = useCallback(() => {}, [])\n  const error = new Error(message)\n  const formattedMessage = useMemo(\n    () => getErrorTextFromBuildErrorMessage(message) || 'Failed to compile',\n    [message]\n  )\n\n  return (\n    <ErrorOverlayLayout\n      errorType=\"Build Error\"\n      errorMessage={formattedMessage}\n      onClose={noop}\n      error={error}\n      footerMessage=\"This error occurred during the build process and can only be dismissed by fixing the error.\"\n      {...props}\n    >\n      <Terminal content={message} />\n    </ErrorOverlayLayout>\n  )\n}\n\nexport const styles = ``\n"], "names": ["BuildError", "styles", "getErrorTextFromBuildErrorMessage", "multiLineMessage", "lines", "split", "stripAnsi", "message", "props", "noop", "useCallback", "error", "Error", "formattedMessage", "useMemo", "ErrorOverlayLayout", "errorType", "errorMessage", "onClose", "footerMessage", "Terminal", "content"], "mappings": ";;;;;;;;;;;;;;;IAwBaA,UAAU;eAAVA;;IAyBAC,MAAM;eAANA;;;;;;iEAjD+B;oEACtB;0BACG;oCACU;AAOnC,MAAMC,oCAAoC,CAACC;IACzC,MAAMC,QAAQD,iBAAiBE,KAAK,CAAC;IACrC,iDAAiD;IACjD,4CAA4C;IAC5C,kBAAkB;IAClB,4CAA4C;IAC5C,OAAO;IACP,wBAAwB;IACxB,mBAAmB;IACnB,qBAAqB;IACrB,MAAM;IACN,OAAOC,IAAAA,kBAAS,EAACF,KAAK,CAAC,EAAE,IAAI;AAC/B;AAEO,MAAMJ,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEO,OAAO,EACP,GAAGC,OACJ,GAHwE;IAIvE,MAAMC,OAAOC,IAAAA,kBAAW,EAAC,KAAO,GAAG,EAAE;IACrC,MAAMC,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/B,MAAMM,mBAAmBC,IAAAA,cAAO,EAC9B,IAAMZ,kCAAkCK,YAAY,qBACpD;QAACA;KAAQ;IAGX,qBACE,qBAACQ,sCAAkB;QACjBC,WAAU;QACVC,cAAcJ;QACdK,SAAST;QACTE,OAAOA;QACPQ,eAAc;QACb,GAAGX,KAAK;kBAET,cAAA,qBAACY,kBAAQ;YAACC,SAASd;;;AAGzB;AAEO,MAAMN,SAAU"}