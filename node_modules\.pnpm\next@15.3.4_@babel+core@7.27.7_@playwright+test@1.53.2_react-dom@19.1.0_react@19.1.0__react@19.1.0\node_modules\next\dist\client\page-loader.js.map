{"version": 3, "sources": ["../../src/client/page-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { RouteLoader } from './route-loader'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport { addBasePath } from './add-base-path'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { addLocale } from './add-locale'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../shared/lib/router/utils/parse-relative-url'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { createRouteLoader, getClientBuildManifest } from './route-loader'\nimport {\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\n\ndeclare global {\n  interface Window {\n    __DEV_MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __DEV_PAGES_MANIFEST?: { pages: string[] }\n    __SSG_MANIFEST_CB?: () => void\n    __SSG_MANIFEST?: Set<string>\n  }\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n  private promisedSsgManifest: Promise<Set<string>>\n  private promisedDevPagesManifest?: Promise<string[]>\n  private promisedMiddlewareMatchers?: Promise<MiddlewareMatcher[]>\n\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if (window.__SSG_MANIFEST) {\n        resolve(window.__SSG_MANIFEST)\n      } else {\n        window.__SSG_MANIFEST_CB = () => {\n          resolve(window.__SSG_MANIFEST!)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if (window.__DEV_PAGES_MANIFEST) {\n        return window.__DEV_PAGES_MANIFEST.pages\n      } else {\n        this.promisedDevPagesManifest ||= fetch(\n          `${this.assetPrefix}/_next/static/development/${DEV_CLIENT_PAGES_MANIFEST}`,\n          { credentials: 'same-origin' }\n        )\n          .then((res) => res.json())\n          .then((manifest: { pages: string[] }) => {\n            window.__DEV_PAGES_MANIFEST = manifest\n            return manifest.pages\n          })\n          .catch((err) => {\n            console.log(`Failed to fetch devPagesManifest:`, err)\n            throw new Error(\n              `Failed to fetch _devPagesManifest.json. Is something blocking that network request?\\n` +\n                'Read more: https://nextjs.org/docs/messages/failed-to-fetch-devpagesmanifest'\n            )\n          })\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  getMiddleware() {\n    // Webpack production\n    if (\n      process.env.NODE_ENV === 'production' &&\n      process.env.__NEXT_MIDDLEWARE_MATCHERS\n    ) {\n      const middlewareMatchers = process.env.__NEXT_MIDDLEWARE_MATCHERS\n      window.__MIDDLEWARE_MATCHERS = middlewareMatchers\n        ? (middlewareMatchers as any as MiddlewareMatcher[])\n        : undefined\n      return window.__MIDDLEWARE_MATCHERS\n      // Turbopack production\n    } else if (process.env.NODE_ENV === 'production') {\n      if (window.__MIDDLEWARE_MATCHERS) {\n        return window.__MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n      // Development both Turbopack and Webpack\n    } else {\n      if (window.__DEV_MIDDLEWARE_MATCHERS) {\n        return window.__DEV_MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__DEV_MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n    }\n  }\n\n  getDataHref(params: {\n    asPath: string\n    href: string\n    locale?: string | false\n    skipInterpolation?: boolean\n  }): string {\n    const { asPath, href, locale } = params\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = removeTrailingSlash(hrefPathname)\n    if (route[0] !== '/') {\n      throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n    }\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removeTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${search}`,\n        true\n      )\n    }\n\n    return getHrefForSlug(\n      params.skipInterpolation\n        ? asPathname\n        : isDynamicRoute(route)\n          ? interpolateAs(hrefPathname, asPathname, query).result\n          : route\n    )\n  }\n\n  _isSsg(\n    /** the route (file-system path) */\n    route: string\n  ): Promise<boolean> {\n    return this.promisedSsgManifest.then((manifest) => manifest.has(route))\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "getClientBuildManifest", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "DEV_CLIENT_PAGES_MANIFEST", "credentials", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "__NEXT_MIDDLEWARE_MATCHERS", "middlewareMatchers", "__MIDDLEWARE_MATCHERS", "undefined", "promisedMiddlewareMatchers", "buildId", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "matchers", "__DEV_MIDDLEWARE_MATCHERS", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "parseRelativeUrl", "asPathname", "route", "removeTrailingSlash", "getHrefForSlug", "path", "dataRoute", "getAssetPathFromRoute", "addLocale", "addBasePath", "skipInterpolation", "isDynamicRoute", "interpolateAs", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "createRouteLoader", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": ";;;;;;;eAiCqBA;;;;6BA9BO;+BACE;gFACI;2BACR;2BACK;kCACE;qCACG;6BACsB;2BAKnD;AAkBQ,MAAMA;IA0BnBC,cAAc;QACZ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,OAAOC,IAAAA,mCAAsB,IAAGC,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW;QACzE,OAAO;YACL,IAAIC,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,6BAAL,IAAI,CAACA,2BAA6BC,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,+BAA4BC,oCAAyB,EACzE;oBAAEC,aAAa;gBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCQ,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,qBAGL,CAHK,IAAIG,MACR,AAAC,0FACC,iFAFE,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;gBACF,OAAO,IAAI,CAACX,wBAAwB;YACtC;QACF;IACF;IAEAY,gBAAgB;QACd,qBAAqB;QACrB,IACEtB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACsB,0BAA0B,EACtC;YACA,MAAMC,qBAAqBxB,QAAQC,GAAG,CAACsB,0BAA0B;YACjEhB,OAAOkB,qBAAqB,GAAGD,qBAC1BA,qBACDE;YACJ,OAAOnB,OAAOkB,qBAAqB;QACnC,uBAAuB;QACzB,OAAO,IAAIzB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YAChD,IAAIK,OAAOkB,qBAAqB,EAAE;gBAChC,OAAOlB,OAAOkB,qBAAqB;YACrC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACE,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGhB,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACgB,OAAO,GAAC,MAAGC,+CAAoC,EACxF;wBAAEf,aAAa;oBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAAC0B;wBACLvB,OAAOkB,qBAAqB,GAAGK;wBAC/B,OAAOA;oBACT,GACCb,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACS,0BAA0B;YACxC;QACA,yCAAyC;QAC3C,OAAO;YACL,IAAIpB,OAAOwB,yBAAyB,EAAE;gBACpC,OAAOxB,OAAOwB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACJ,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGhB,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACgB,OAAO,GAAC,MAAGI,yCAA8B,EAClF;wBAAElB,aAAa;oBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAAC0B;wBACLvB,OAAOwB,yBAAyB,GAAGD;wBACnC,OAAOA;oBACT,GACCb,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACS,0BAA0B;YACxC;QACF;IACF;IAEAM,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,IAAAA,kCAAgB,EAACN;QACnE,MAAM,EAAEE,UAAUK,UAAU,EAAE,GAAGD,IAAAA,kCAAgB,EAACP;QAClD,MAAMS,QAAQC,IAAAA,wCAAmB,EAACN;QAClC,IAAIK,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,qBAA+D,CAA/D,IAAIvB,MAAM,AAAC,8CAA2CuB,QAAM,MAA5D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;QAEA,MAAME,iBAAiB,CAACC;YACtB,MAAMC,YAAYC,IAAAA,8BAAqB,EACrCJ,IAAAA,wCAAmB,EAACK,IAAAA,oBAAS,EAACH,MAAMV,UACpC;YAEF,OAAOc,IAAAA,wBAAW,EAChB,AAAC,iBAAc,IAAI,CAACvB,OAAO,GAAGoB,YAAYP,QAC1C;QAEJ;QAEA,OAAOK,eACLZ,OAAOkB,iBAAiB,GACpBT,aACAU,IAAAA,yBAAc,EAACT,SACbU,IAAAA,4BAAa,EAACf,cAAcI,YAAYH,OAAOe,MAAM,GACrDX;IAEV;IAEAY,OACE,iCAAiC,GACjCZ,KAAa,EACK;QAClB,OAAO,IAAI,CAACa,mBAAmB,CAACrD,IAAI,CAAC,CAACC,WAAaA,SAASqD,GAAG,CAACd;IAClE;IAEAe,SAASf,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACgB,WAAW,CAACC,SAAS,CAACjB,OAAOxC,IAAI,CAAC,CAACW;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACL+C,MAAM/C,IAAIgD,SAAS;oBACnBC,KAAKjD,IAAIkD,OAAO;oBAChBC,aAAanD,IAAIoD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClCjC,MAAMiC,EAAEjC,IAAI;4BACZkC,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAMxD,IAAIyD,KAAK;QACjB;IACF;IAEAC,SAAS7B,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACgB,WAAW,CAACa,QAAQ,CAAC7B;IACnC;IAtKA8B,YAAY9C,OAAe,EAAEhB,WAAmB,CAAE;QAChD,IAAI,CAACgD,WAAW,GAAGe,IAAAA,8BAAiB,EAAC/D;QAErC,IAAI,CAACgB,OAAO,GAAGA;QACf,IAAI,CAAChB,WAAW,GAAGA;QAEnB,IAAI,CAAC6C,mBAAmB,GAAG,IAAImB,QAAQ,CAACC;YACtC,IAAItE,OAAOuE,cAAc,EAAE;gBACzBD,QAAQtE,OAAOuE,cAAc;YAC/B,OAAO;gBACLvE,OAAOwE,iBAAiB,GAAG;oBACzBF,QAAQtE,OAAOuE,cAAc;gBAC/B;YACF;QACF;IACF;AAwJF"}