{"version": 3, "sources": ["../../../../src/client/components/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs, parseConsoleArgs } from '../../lib/console'\nimport isError from '../../../lib/is-error'\nimport { createConsoleError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from '../errors/stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleConsoleError(\n  originError: unknown,\n  consoleErrorArgs: any[]\n) {\n  let error: Error\n  const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n  if (isError(originError)) {\n    error = createConsoleError(originError, environmentName)\n  } else {\n    error = createConsoleError(\n      formatConsoleArgs(consoleErrorArgs),\n      environmentName\n    )\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function handleClientError(originError: unknown) {\n  let error: Error\n  if (isError(originError)) {\n    error = originError\n  } else {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = originError + ''\n    error = new Error(formattedErrorMessage)\n  }\n  error = getReactStitchedError(error)\n\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (event.error) {\n    handleClientError(event.error)\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = new Error(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["handleClientError", "handleConsoleError", "handleGlobalErrors", "useErrorHandler", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "originError", "consoleErrorArgs", "error", "environmentName", "parseConsoleArgs", "isError", "createConsoleError", "formatConsoleArgs", "getReactStitchedError", "storeHydrationErrorStateFromConsoleArgs", "attachHydrationErrorState", "enqueueConsecutiveDedupedError", "handler", "formattedErrorMessage", "Error", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "push", "splice", "indexOf", "length", "onUnhandledError", "event", "isNextRouterError", "preventDefault", "onUnhandledRejection", "ev", "reason", "window", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;;;;;;;;;;;;;IAiDgBA,iBAAiB;eAAjBA;;IA7BAC,kBAAkB;eAAlBA;;IA8GAC,kBAAkB;eAAlBA;;IA1DAC,eAAe;eAAfA;;;;uBAxEU;2CACgB;mCACR;oCACsB;yBACJ;kEAChC;8BACe;oCACY;+BACT;AAEtC,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASb,mBACdc,WAAoB,EACpBC,gBAAuB;IAEvB,IAAIC;IACJ,MAAM,EAAEC,eAAe,EAAE,GAAGC,IAAAA,yBAAgB,EAACH;IAC7C,IAAII,IAAAA,gBAAO,EAACL,cAAc;QACxBE,QAAQI,IAAAA,gCAAkB,EAACN,aAAaG;IAC1C,OAAO;QACLD,QAAQI,IAAAA,gCAAkB,EACxBC,IAAAA,0BAAiB,EAACN,mBAClBE;IAEJ;IACAD,QAAQM,IAAAA,oCAAqB,EAACN;IAE9BO,IAAAA,2DAAuC,KAAIR;IAC3CS,IAAAA,oDAAyB,EAACR;IAE1BS,IAAAA,kDAA8B,EAACf,YAAYM;IAC3C,KAAK,MAAMU,WAAWf,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbuB,QAAQV;QACV;IACF;AACF;AAEO,SAASjB,kBAAkBe,WAAoB;IACpD,IAAIE;IACJ,IAAIG,IAAAA,gBAAO,EAACL,cAAc;QACxBE,QAAQF;IACV,OAAO;QACL,sDAAsD;QACtD,MAAMa,wBAAwBb,cAAc;QAC5CE,QAAQ,qBAAgC,CAAhC,IAAIY,MAAMD,wBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+B;IACzC;IACAX,QAAQM,IAAAA,oCAAqB,EAACN;IAE9BQ,IAAAA,oDAAyB,EAACR;IAE1BS,IAAAA,kDAA8B,EAACf,YAAYM;IAC3C,KAAK,MAAMU,WAAWf,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbuB,QAAQV;QACV;IACF;AACF;AAEO,SAASd,gBACd2B,sBAAoC,EACpCC,0BAAwC;IAExCC,IAAAA,gBAAS,EAAC;QACR,wBAAwB;QACxBrB,WAAWsB,OAAO,CAACH;QACnBjB,eAAeoB,OAAO,CAACF;QAEvB,wBAAwB;QACxBnB,cAAcsB,IAAI,CAACJ;QACnBhB,kBAAkBoB,IAAI,CAACH;QAEvB,OAAO;YACL,oBAAoB;YACpBnB,cAAcuB,MAAM,CAACvB,cAAcwB,OAAO,CAACN,yBAAyB;YACpEhB,kBAAkBqB,MAAM,CACtBrB,kBAAkBsB,OAAO,CAACL,6BAC1B;YAGF,sBAAsB;YACtBpB,WAAWwB,MAAM,CAAC,GAAGxB,WAAW0B,MAAM;YACtCxB,eAAesB,MAAM,CAAC,GAAGtB,eAAewB,MAAM;QAChD;IACF,GAAG;QAACP;QAAwBC;KAA2B;AACzD;AAEA,SAASO,iBAAiBC,KAA8B;IACtD,IAAIC,IAAAA,oCAAiB,EAACD,MAAMtB,KAAK,GAAG;QAClCsB,MAAME,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAIF,MAAMtB,KAAK,EAAE;QACfjB,kBAAkBuC,MAAMtB,KAAK;IAC/B;AACF;AAEA,SAASyB,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIJ,IAAAA,oCAAiB,EAACI,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIxB,QAAQ2B;IACZ,IAAI3B,SAAS,CAACG,IAAAA,gBAAO,EAACH,QAAQ;QAC5BA,QAAQ,qBAAqB,CAArB,IAAIY,MAAMZ,QAAQ,KAAlB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoB;IAC9B;IAEAJ,eAAeqB,IAAI,CAACjB;IACpB,KAAK,MAAMU,WAAWb,kBAAmB;QACvCa,QAAQV;IACV;AACF;AAEO,SAASf;IACd,IAAI,OAAO2C,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDhB,MAAMiB,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETD,OAAOE,gBAAgB,CAAC,SAAST;QACjCO,OAAOE,gBAAgB,CAAC,sBAAsBL;IAChD;AACF"}