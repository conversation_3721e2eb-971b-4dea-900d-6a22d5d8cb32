export declare const HTTPAccessErrorStatus: {
    NOT_FOUND: number;
    FORBIDDEN: number;
    UNAUTHORIZED: number;
};
export declare const HTTP_ERROR_FALLBACK_ERROR_CODE = "NEXT_HTTP_ERROR_FALLBACK";
export type HTTPAccessFallbackError = Error & {
    digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`;
};
/**
 * Checks an error to determine if it's an error generated by
 * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.
 *
 * @param error the error that may reference a HTTP access error
 * @returns true if the error is a HTTP access error
 */
export declare function isHTTPAccessFallbackError(error: unknown): error is HTTPAccessFallbackError;
export declare function getAccessFallbackHTTPStatus(error: HTTPAccessFallbackError): number;
export declare function getAccessFallbackErrorTypeByStatus(status: number): 'not-found' | 'forbidden' | 'unauthorized' | undefined;
