# cli-cursor

> Toggle the CLI cursor

The cursor is [gracefully restored](https://github.com/sindresorhus/restore-cursor) if the process exits.

## Install

```sh
npm install cli-cursor
```

## Usage

```js
import cliCursor from 'cli-cursor';

cliCursor.hide();

const unicornsAreAwesome = true;
cliCursor.toggle(unicornsAreAwesome);
```

## API

### .show(stream?)

### .hide(stream?)

### .toggle(force?, stream?)

#### force

Useful for showing or hiding the cursor based on a boolean.

#### stream

Type: `stream.Writable`\
Default: `process.stderr`
