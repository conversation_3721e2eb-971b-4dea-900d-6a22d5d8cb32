{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/ui/dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\n\nimport { ShadowPortal } from './components/shadow-portal'\nimport { Base } from './styles/base'\nimport { ComponentStyles } from './styles/component-styles'\nimport { CssReset } from './styles/css-reset'\nimport { Colors } from './styles/colors'\nimport { ErrorOverlay } from './components/errors/error-overlay/error-overlay'\nimport { DevToolsIndicator } from './components/errors/dev-tools-indicator/dev-tools-indicator'\nimport { RenderError } from './container/runtime-error/render-error'\nimport { DarkTheme } from './styles/dark-theme'\nimport { useDevToolsScale } from './components/errors/dev-tools-indicator/dev-tools-info/preferences'\n\nexport function DevOverlay({\n  state,\n  isErrorOverlayOpen,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  isErrorOverlayOpen: boolean\n  setIsErrorOverlayOpen: (\n    isErrorOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n}) {\n  const [scale, setScale] = useDevToolsScale()\n  return (\n    <ShadowPortal>\n      <CssReset />\n      <Base scale={scale} />\n      <Colors />\n      <ComponentStyles />\n      <DarkTheme />\n\n      <RenderError state={state} isAppDir={true}>\n        {({ runtimeErrors, totalErrorCount }) => {\n          const isBuildError = state.buildError !== null\n          return (\n            <>\n              {state.showIndicator && (\n                <DevToolsIndicator\n                  scale={scale}\n                  setScale={setScale}\n                  state={state}\n                  errorCount={totalErrorCount}\n                  isBuildError={isBuildError}\n                  setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n                />\n              )}\n\n              <ErrorOverlay\n                state={state}\n                runtimeErrors={runtimeErrors}\n                isErrorOverlayOpen={isErrorOverlayOpen}\n                setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n              />\n            </>\n          )\n        }}\n      </RenderError>\n    </ShadowPortal>\n  )\n}\n"], "names": ["DevOverlay", "state", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "scale", "setScale", "useDevToolsScale", "ShadowPort<PERSON>", "CssReset", "Base", "Colors", "ComponentStyles", "DarkTheme", "RenderError", "isAppDir", "runtimeErrors", "totalErrorCount", "isBuildError", "buildError", "showIndicator", "DevToolsIndicator", "errorCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;+BAagBA;;;eAAAA;;;;8BAXa;sBACR;iCACW;0BACP;wBACF;8BACM;mCACK;6BACN;2BACF;6BACO;AAE1B,SAASA,WAAW,KAU1B;IAV0B,IAAA,EACzBC,KAAK,EACLC,kBAAkB,EAClBC,qBAAqB,EAOtB,GAV0B;IAWzB,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,6BAAgB;IAC1C,qBACE,sBAACC,0BAAY;;0BACX,qBAACC,kBAAQ;0BACT,qBAACC,UAAI;gBAACL,OAAOA;;0BACb,qBAACM,cAAM;0BACP,qBAACC,gCAAe;0BAChB,qBAACC,oBAAS;0BAEV,qBAACC,wBAAW;gBAACZ,OAAOA;gBAAOa,UAAU;0BAClC;wBAAC,EAAEC,aAAa,EAAEC,eAAe,EAAE;oBAClC,MAAMC,eAAehB,MAAMiB,UAAU,KAAK;oBAC1C,qBACE;;4BACGjB,MAAMkB,aAAa,kBAClB,qBAACC,oCAAiB;gCAChBhB,OAAOA;gCACPC,UAAUA;gCACVJ,OAAOA;gCACPoB,YAAYL;gCACZC,cAAcA;gCACdd,uBAAuBA;;0CAI3B,qBAACmB,0BAAY;gCACXrB,OAAOA;gCACPc,eAAeA;gCACfb,oBAAoBA;gCACpBC,uBAAuBA;;;;gBAI/B;;;;AAIR"}