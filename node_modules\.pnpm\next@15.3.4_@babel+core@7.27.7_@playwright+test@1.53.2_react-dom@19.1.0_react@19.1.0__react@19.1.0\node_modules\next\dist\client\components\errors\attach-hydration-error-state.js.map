{"version": 3, "sources": ["../../../../src/client/components/errors/attach-hydration-error-state.ts"], "sourcesContent": ["import {\n  getDefaultHydrationErrorMessage,\n  isHydrationError,\n  testReactHydrationWarning,\n} from '../is-hydration-error'\nimport {\n  hydrationErrorState,\n  getReactHydrationDiffSegments,\n  type HydrationErrorState,\n} from './hydration-error-info'\n\nexport function attachHydrationErrorState(error: Error) {\n  let parsedHydrationErrorState: typeof hydrationErrorState = {}\n  const isHydrationWarning = testReactHydrationWarning(error.message)\n  const isHydrationRuntimeError = isHydrationError(error)\n\n  // If it's not hydration warnings or errors, skip\n  if (!(isHydrationRuntimeError || isHydrationWarning)) {\n    return\n  }\n\n  const reactHydrationDiffSegments = getReactHydrationDiffSegments(\n    error.message\n  )\n  // If the reactHydrationDiffSegments exists\n  // and the diff (reactHydrationDiffSegments[1]) exists\n  // e.g. the hydration diff log error.\n  if (reactHydrationDiffSegments) {\n    const diff = reactHydrationDiffSegments[1]\n    parsedHydrationErrorState = {\n      ...((error as any).details as HydrationErrorState),\n      ...hydrationErrorState,\n      // If diff is present in error, we don't need to pick up the console logged warning.\n      // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n      // - if hydration error no diff, then leverage the one from the hydration diff log.\n\n      warning: (diff && !isHydrationWarning\n        ? null\n        : hydrationErrorState.warning) || [\n        getDefaultHydrationErrorMessage(),\n        '',\n        '',\n      ],\n      // When it's hydration diff log, do not show notes section.\n      // This condition is only for the 1st squashed error.\n      notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n      reactOutputComponentDiff: diff,\n    }\n    // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (!hydrationErrorState.reactOutputComponentDiff && diff) {\n      hydrationErrorState.reactOutputComponentDiff = diff\n    }\n    // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n    if (\n      !diff &&\n      isHydrationRuntimeError &&\n      hydrationErrorState.reactOutputComponentDiff\n    ) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  } else {\n    // Normal runtime error, where it doesn't contain the hydration diff.\n\n    // If there's any extra information in the error message to display,\n    // append it to the error message details property\n    if (hydrationErrorState.warning) {\n      // The patched console.error found hydration errors logged by React\n      // Append the logged warning to the error message\n      parsedHydrationErrorState = {\n        ...(error as any).details,\n        // It contains the warning, component stack, server and client tag names\n        ...hydrationErrorState,\n      }\n    }\n    // Consume the cached hydration diff.\n    // This is only required for now when we still squashed the hydration diff log into hydration error.\n    // Once the all error is logged to dev overlay in order, this will go away.\n    if (hydrationErrorState.reactOutputComponentDiff) {\n      parsedHydrationErrorState.reactOutputComponentDiff =\n        hydrationErrorState.reactOutputComponentDiff\n    }\n  }\n  // If it's a hydration error, store the hydration error state into the error object\n  ;(error as any).details = parsedHydrationErrorState\n}\n"], "names": ["attachHydrationErrorState", "error", "parsedHydrationErrorState", "isHydrationWarning", "testReactHydrationWarning", "message", "isHydrationRuntimeError", "isHydrationError", "reactHydrationDiffSegments", "getReactHydrationDiffSegments", "diff", "details", "hydrationErrorState", "warning", "getDefaultHydrationErrorMessage", "notes", "reactOutputComponentDiff"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;kCAPT;oCAKA;AAEA,SAASA,0BAA0BC,KAAY;IACpD,IAAIC,4BAAwD,CAAC;IAC7D,MAAMC,qBAAqBC,IAAAA,2CAAyB,EAACH,MAAMI,OAAO;IAClE,MAAMC,0BAA0BC,IAAAA,kCAAgB,EAACN;IAEjD,iDAAiD;IACjD,IAAI,CAAEK,CAAAA,2BAA2BH,kBAAiB,GAAI;QACpD;IACF;IAEA,MAAMK,6BAA6BC,IAAAA,iDAA6B,EAC9DR,MAAMI,OAAO;IAEf,2CAA2C;IAC3C,sDAAsD;IACtD,qCAAqC;IACrC,IAAIG,4BAA4B;QAC9B,MAAME,OAAOF,0BAA0B,CAAC,EAAE;QAC1CN,4BAA4B;YAC1B,GAAI,AAACD,MAAcU,OAAO;YAC1B,GAAGC,uCAAmB;YACtB,oFAAoF;YACpF,oGAAoG;YACpG,mFAAmF;YAEnFC,SAAS,AAACH,CAAAA,QAAQ,CAACP,qBACf,OACAS,uCAAmB,CAACC,OAAO,AAAD,KAAM;gBAClCC,IAAAA,iDAA+B;gBAC/B;gBACA;aACD;YACD,2DAA2D;YAC3D,qDAAqD;YACrDC,OAAOZ,qBAAqB,KAAKK,0BAA0B,CAAC,EAAE;YAC9DQ,0BAA0BN;QAC5B;QACA,iEAAiE;QACjE,oGAAoG;QACpG,2EAA2E;QAC3E,IAAI,CAACE,uCAAmB,CAACI,wBAAwB,IAAIN,MAAM;YACzDE,uCAAmB,CAACI,wBAAwB,GAAGN;QACjD;QACA,kHAAkH;QAClH,IACE,CAACA,QACDJ,2BACAM,uCAAmB,CAACI,wBAAwB,EAC5C;YACAd,0BAA0Bc,wBAAwB,GAChDJ,uCAAmB,CAACI,wBAAwB;QAChD;IACF,OAAO;QACL,qEAAqE;QAErE,oEAAoE;QACpE,kDAAkD;QAClD,IAAIJ,uCAAmB,CAACC,OAAO,EAAE;YAC/B,mEAAmE;YACnE,iDAAiD;YACjDX,4BAA4B;gBAC1B,GAAG,AAACD,MAAcU,OAAO;gBACzB,wEAAwE;gBACxE,GAAGC,uCAAmB;YACxB;QACF;QACA,qCAAqC;QACrC,oGAAoG;QACpG,2EAA2E;QAC3E,IAAIA,uCAAmB,CAACI,wBAAwB,EAAE;YAChDd,0BAA0Bc,wBAAwB,GAChDJ,uCAAmB,CAACI,wBAAwB;QAChD;IACF;IACA,mFAAmF;;IACjFf,MAAcU,OAAO,GAAGT;AAC5B"}