{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { stringBufferUtils } from 'next/dist/compiled/webpack-sources3'\nimport { red } from '../../lib/picocolors'\nimport formatWebpackMessages from '../../client/components/react-dev-overlay/utils/format-webpack-messages'\nimport { nonNullable } from '../../lib/non-nullable'\nimport type { COMPILER_INDEXES } from '../../shared/lib/constants'\nimport {\n  COMPILER_NAMES,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  APP_CLIENT_INTERNALS,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport { runCompiler } from '../compiler'\nimport * as Log from '../output/log'\nimport getBaseWebpackConfig, { loadProjectInfo } from '../webpack-config'\nimport type { NextError } from '../../lib/is-error'\nimport {\n  TelemetryPlugin,\n  type TelemetryPluginState,\n} from '../webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport {\n  NextBuildContext,\n  resumePluginState,\n  getPluginState,\n} from '../build-context'\nimport { createEntrypoints } from '../entries'\nimport loadConfig from '../../server/config'\nimport {\n  getTraceEvents,\n  initializeTraceState,\n  setGlobal,\n  trace,\n  type TraceEvent,\n  type TraceState,\n} from '../../trace'\nimport { WEBPACK_LAYERS } from '../../lib/constants'\nimport { TraceEntryPointsPlugin } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport type { BuildTraceContext } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\n\nimport origDebug from 'next/dist/compiled/debug'\nimport { Telemetry } from '../../telemetry/storage'\nimport { durationToString } from '../duration-to-string'\n\nconst debug = origDebug('next:build:webpack-build')\n\ntype CompilerResult = {\n  errors: webpack.StatsError[]\n  warnings: webpack.StatsError[]\n  stats: (webpack.Stats | undefined)[]\n}\n\ntype SingleCompilerResult = {\n  errors: webpack.StatsError[]\n  warnings: webpack.StatsError[]\n  stats: webpack.Stats | undefined\n}\n\nfunction isTelemetryPlugin(plugin: unknown): plugin is TelemetryPlugin {\n  return plugin instanceof TelemetryPlugin\n}\n\nfunction isTraceEntryPointsPlugin(\n  plugin: unknown\n): plugin is TraceEntryPointsPlugin {\n  return plugin instanceof TraceEntryPointsPlugin\n}\n\nexport async function webpackBuildImpl(\n  compilerName: keyof typeof COMPILER_INDEXES | null\n): Promise<{\n  duration: number\n  pluginState: any\n  buildTraceContext?: BuildTraceContext\n  telemetryState?: TelemetryPluginState\n}> {\n  let result: CompilerResult | null = {\n    warnings: [],\n    errors: [],\n    stats: [],\n  }\n  let webpackBuildStart\n  const nextBuildSpan = NextBuildContext.nextBuildSpan!\n  const dir = NextBuildContext.dir!\n  const config = NextBuildContext.config!\n  process.env.NEXT_COMPILER_NAME = compilerName || 'server'\n\n  const runWebpackSpan = nextBuildSpan.traceChild('run-webpack-compiler')\n  const entrypoints = await nextBuildSpan\n    .traceChild('create-entrypoints')\n    .traceAsyncFn(() =>\n      createEntrypoints({\n        buildId: NextBuildContext.buildId!,\n        config: config,\n        envFiles: NextBuildContext.loadedEnvFiles!,\n        isDev: false,\n        rootDir: dir,\n        pageExtensions: config.pageExtensions!,\n        pagesDir: NextBuildContext.pagesDir!,\n        appDir: NextBuildContext.appDir!,\n        pages: NextBuildContext.mappedPages!,\n        appPaths: NextBuildContext.mappedAppPages!,\n        previewMode: NextBuildContext.previewProps!,\n        rootPaths: NextBuildContext.mappedRootPaths!,\n        hasInstrumentationHook: NextBuildContext.hasInstrumentationHook!,\n      })\n    )\n\n  const commonWebpackOptions = {\n    isServer: false,\n    isCompileMode: NextBuildContext.isCompileMode,\n    buildId: NextBuildContext.buildId!,\n    encryptionKey: NextBuildContext.encryptionKey!,\n    config: config,\n    appDir: NextBuildContext.appDir!,\n    pagesDir: NextBuildContext.pagesDir!,\n    rewrites: NextBuildContext.rewrites!,\n    originalRewrites: NextBuildContext.originalRewrites,\n    originalRedirects: NextBuildContext.originalRedirects,\n    reactProductionProfiling: NextBuildContext.reactProductionProfiling!,\n    noMangling: NextBuildContext.noMangling!,\n    clientRouterFilters: NextBuildContext.clientRouterFilters!,\n    previewModeId: NextBuildContext.previewModeId!,\n    allowedRevalidateHeaderKeys: NextBuildContext.allowedRevalidateHeaderKeys!,\n    fetchCacheKeyPrefix: NextBuildContext.fetchCacheKeyPrefix!,\n  }\n\n  const configs = await runWebpackSpan\n    .traceChild('generate-webpack-config')\n    .traceAsyncFn(async () => {\n      const info = await loadProjectInfo({\n        dir,\n        config: commonWebpackOptions.config,\n        dev: false,\n      })\n      return Promise.all([\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          runWebpackSpan,\n          compilerType: COMPILER_NAMES.client,\n          entrypoints: entrypoints.client,\n          ...info,\n        }),\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          runWebpackSpan,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          compilerType: COMPILER_NAMES.server,\n          entrypoints: entrypoints.server,\n          ...info,\n        }),\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          runWebpackSpan,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          compilerType: COMPILER_NAMES.edgeServer,\n          entrypoints: entrypoints.edgeServer,\n          edgePreviewProps: {\n            __NEXT_PREVIEW_MODE_ID:\n              NextBuildContext.previewProps!.previewModeId,\n            __NEXT_PREVIEW_MODE_ENCRYPTION_KEY:\n              NextBuildContext.previewProps!.previewModeEncryptionKey,\n            __NEXT_PREVIEW_MODE_SIGNING_KEY:\n              NextBuildContext.previewProps!.previewModeSigningKey,\n          },\n          ...info,\n        }),\n      ])\n    })\n\n  const clientConfig = configs[0]\n  const serverConfig = configs[1]\n  const edgeConfig = configs[2]\n\n  if (\n    clientConfig.optimization &&\n    (clientConfig.optimization.minimize !== true ||\n      (clientConfig.optimization.minimizer &&\n        clientConfig.optimization.minimizer.length === 0))\n  ) {\n    Log.warn(\n      `Production code optimization has been disabled in your project. Read more: https://nextjs.org/docs/messages/minification-disabled`\n    )\n  }\n\n  webpackBuildStart = process.hrtime()\n\n  debug(`starting compiler`, compilerName)\n  // We run client and server compilation separately to optimize for memory usage\n  await runWebpackSpan.traceAsyncFn(async () => {\n    if (config.experimental.webpackMemoryOptimizations) {\n      stringBufferUtils.disableDualStringBufferCaching()\n      stringBufferUtils.enterStringInterningRange()\n    }\n\n    // Run the server compilers first and then the client\n    // compiler to track the boundary of server/client components.\n    let clientResult: SingleCompilerResult | null = null\n\n    // During the server compilations, entries of client components will be\n    // injected to this set and then will be consumed by the client compiler.\n    let serverResult: UnwrapPromise<ReturnType<typeof runCompiler>>[0] | null =\n      null\n    let edgeServerResult:\n      | UnwrapPromise<ReturnType<typeof runCompiler>>[0]\n      | null = null\n\n    let inputFileSystem: webpack.Compiler['inputFileSystem'] | undefined\n\n    if (!compilerName || compilerName === 'server') {\n      debug('starting server compiler')\n      const start = Date.now()\n      ;[serverResult, inputFileSystem] = await runCompiler(serverConfig, {\n        runWebpackSpan,\n        inputFileSystem,\n      })\n      debug(`server compiler finished ${Date.now() - start}ms`)\n    }\n\n    if (!compilerName || compilerName === 'edge-server') {\n      debug('starting edge-server compiler')\n      const start = Date.now()\n      ;[edgeServerResult, inputFileSystem] = edgeConfig\n        ? await runCompiler(edgeConfig, { runWebpackSpan, inputFileSystem })\n        : [null]\n      debug(`edge-server compiler finished ${Date.now() - start}ms`)\n    }\n\n    // Only continue if there were no errors\n    if (!serverResult?.errors.length && !edgeServerResult?.errors.length) {\n      const pluginState = getPluginState()\n      for (const key in pluginState.injectedClientEntries) {\n        const value = pluginState.injectedClientEntries[key]\n        const clientEntry = clientConfig.entry as webpack.EntryObject\n        if (key === APP_CLIENT_INTERNALS) {\n          clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP] = {\n            import: [\n              // TODO-APP: cast clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP] to type EntryDescription once it's available from webpack\n              // @ts-expect-error clientEntry['main-app'] is type EntryDescription { import: ... }\n              ...clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP].import,\n              value,\n            ],\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n          }\n        } else {\n          clientEntry[key] = {\n            dependOn: [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP],\n            import: value,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n          }\n        }\n      }\n\n      if (!compilerName || compilerName === 'client') {\n        debug('starting client compiler')\n        const start = Date.now()\n        ;[clientResult, inputFileSystem] = await runCompiler(clientConfig, {\n          runWebpackSpan,\n          inputFileSystem,\n        })\n        debug(`client compiler finished ${Date.now() - start}ms`)\n      }\n    }\n\n    if (config.experimental.webpackMemoryOptimizations) {\n      stringBufferUtils.exitStringInterningRange()\n    }\n    inputFileSystem?.purge?.()\n\n    result = {\n      warnings: [\n        ...(clientResult?.warnings ?? []),\n        ...(serverResult?.warnings ?? []),\n        ...(edgeServerResult?.warnings ?? []),\n      ].filter(nonNullable),\n      errors: [\n        ...(clientResult?.errors ?? []),\n        ...(serverResult?.errors ?? []),\n        ...(edgeServerResult?.errors ?? []),\n      ].filter(nonNullable),\n      stats: [\n        clientResult?.stats,\n        serverResult?.stats,\n        edgeServerResult?.stats,\n      ],\n    }\n  })\n  result = nextBuildSpan\n    .traceChild('format-webpack-messages')\n    .traceFn(() => formatWebpackMessages(result, true)) as CompilerResult\n\n  const telemetryPlugin = (clientConfig as webpack.Configuration).plugins?.find(\n    isTelemetryPlugin\n  )\n\n  const traceEntryPointsPlugin = (\n    serverConfig as webpack.Configuration\n  ).plugins?.find(isTraceEntryPointsPlugin)\n\n  const webpackBuildEnd = process.hrtime(webpackBuildStart)\n\n  if (result.errors.length > 0) {\n    // Only keep the first few errors. Others are often indicative\n    // of the same problem, but confuse the reader with noise.\n    if (result.errors.length > 5) {\n      result.errors.length = 5\n    }\n    let error = result.errors.filter(Boolean).join('\\n\\n')\n\n    console.error(red('Failed to compile.\\n'))\n\n    if (\n      error.indexOf('private-next-pages') > -1 &&\n      error.indexOf('does not contain a default export') > -1\n    ) {\n      const page_name_regex = /'private-next-pages\\/(?<page_name>[^']*)'/\n      const parsed = page_name_regex.exec(error)\n      const page_name = parsed && parsed.groups && parsed.groups.page_name\n      throw new Error(\n        `webpack build failed: found page without a React Component as default export in pages/${page_name}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.`\n      )\n    }\n\n    console.error(error)\n    console.error()\n\n    if (\n      error.indexOf('private-next-pages') > -1 ||\n      error.indexOf('__next_polyfill__') > -1\n    ) {\n      const err = new Error(\n        'webpack config.resolve.alias was incorrectly overridden. https://nextjs.org/docs/messages/invalid-resolve-alias'\n      ) as NextError\n      err.code = 'INVALID_RESOLVE_ALIAS'\n      throw err\n    }\n    const err = new Error(\n      `Build failed because of ${process.env.NEXT_RSPACK ? 'rspack' : 'webpack'} errors`\n    ) as NextError\n    err.code = 'WEBPACK_ERRORS'\n    throw err\n  } else {\n    const duration = webpackBuildEnd[0]\n    const durationString = durationToString(duration)\n\n    if (result.warnings.length > 0) {\n      Log.warn(`Compiled with warnings in ${durationString}\\n`)\n      console.warn(result.warnings.filter(Boolean).join('\\n\\n'))\n      console.warn()\n    } else if (!compilerName) {\n      Log.event(`Compiled successfully in ${durationString}`)\n    }\n\n    return {\n      duration: webpackBuildEnd[0],\n      buildTraceContext: traceEntryPointsPlugin?.buildTraceContext,\n      pluginState: getPluginState(),\n      telemetryState: {\n        usages: telemetryPlugin?.usages() || [],\n        packagesUsedInServerSideProps:\n          telemetryPlugin?.packagesUsedInServerSideProps() || [],\n        useCacheTracker: telemetryPlugin?.getUseCacheTracker() || {},\n      },\n    }\n  }\n}\n\n// the main function when this file is run as a worker\nexport async function workerMain(workerData: {\n  compilerName: keyof typeof COMPILER_INDEXES\n  buildContext: typeof NextBuildContext\n  traceState: TraceState\n}): Promise<\n  Awaited<ReturnType<typeof webpackBuildImpl>> & {\n    debugTraceEvents: TraceEvent[]\n  }\n> {\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: workerData.buildContext.config!.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  // Initialize tracer state from the parent\n  initializeTraceState(workerData.traceState)\n\n  // Resume plugin state\n  resumePluginState(NextBuildContext.pluginState)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n  NextBuildContext.nextBuildSpan = trace(\n    `worker-main-${workerData.compilerName}`\n  )\n\n  const result = await webpackBuildImpl(workerData.compilerName)\n  const { entriesTrace, chunksTrace } = result.buildTraceContext ?? {}\n  if (entriesTrace) {\n    const { entryNameMap, depModArray } = entriesTrace\n    if (depModArray) {\n      result.buildTraceContext!.entriesTrace!.depModArray = depModArray\n    }\n    if (entryNameMap) {\n      const entryEntries = entryNameMap\n      result.buildTraceContext!.entriesTrace!.entryNameMap = entryEntries\n    }\n  }\n  if (chunksTrace?.entryNameFilesMap) {\n    const entryNameFilesMap = chunksTrace.entryNameFilesMap\n    result.buildTraceContext!.chunksTrace!.entryNameFilesMap = entryNameFilesMap\n  }\n  NextBuildContext.nextBuildSpan.stop()\n  return { ...result, debugTraceEvents: getTraceEvents() }\n}\n"], "names": ["webpackBuildImpl", "worker<PERSON>ain", "debug", "origDebug", "isTelemetryPlugin", "plugin", "TelemetryPlugin", "isTraceEntryPointsPlugin", "TraceEntryPointsPlugin", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "NextBuildContext", "dir", "config", "process", "env", "NEXT_COMPILER_NAME", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "createEntrypoints", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "isCompileMode", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "loadProjectInfo", "dev", "Promise", "all", "getBaseWebpackConfig", "middlewareMatchers", "compilerType", "COMPILER_NAMES", "client", "server", "edgeServer", "edgePreviewProps", "__NEXT_PREVIEW_MODE_ID", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "Log", "warn", "hrtime", "inputFileSystem", "experimental", "webpackMemoryOptimizations", "string<PERSON><PERSON><PERSON><PERSON><PERSON>s", "disableDualStringBufferCaching", "enterStringInterningRange", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "runCompiler", "pluginState", "getPluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "import", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "dependOn", "exitStringInterningRange", "purge", "filter", "nonNullable", "traceFn", "formatWebpackMessages", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "red", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "NEXT_RSPACK", "duration", "durationString", "durationToString", "event", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "getUseCacheTracker", "workerData", "telemetry", "Telemetry", "distDir", "buildContext", "setGlobal", "Object", "assign", "initializeTraceState", "traceState", "resumePluginState", "loadConfig", "PHASE_PRODUCTION_BUILD", "trace", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents", "getTraceEvents"], "mappings": ";;;;;;;;;;;;;;;IAoEsBA,gBAAgB;eAAhBA;;IA6SAC,UAAU;eAAVA;;;iCAhXY;4BACd;8EACc;6BACN;2BAOrB;0BACqB;6DACP;uEACiC;iCAK/C;8BAKA;yBAC2B;+DACX;uBAQhB;4BACwB;4CACQ;8DAIjB;yBACI;kCACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAcxB,SAASC,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBC,gCAAe;AAC1C;AAEA,SAASC,yBACPF,MAAe;IAEf,OAAOA,kBAAkBG,kDAAsB;AACjD;AAEO,eAAeR,iBACpBS,YAAkD;QA+N1B,uBAIO;IA5N/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBC,8BAAgB,CAACD,aAAa;IACpD,MAAME,MAAMD,8BAAgB,CAACC,GAAG;IAChC,MAAMC,SAASF,8BAAgB,CAACE,MAAM;IACtCC,QAAQC,GAAG,CAACC,kBAAkB,GAAGZ,gBAAgB;IAEjD,MAAMa,iBAAiBP,cAAcQ,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAMT,cACvBQ,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZC,IAAAA,0BAAiB,EAAC;YAChBC,SAASX,8BAAgB,CAACW,OAAO;YACjCT,QAAQA;YACRU,UAAUZ,8BAAgB,CAACa,cAAc;YACzCC,OAAO;YACPC,SAASd;YACTe,gBAAgBd,OAAOc,cAAc;YACrCC,UAAUjB,8BAAgB,CAACiB,QAAQ;YACnCC,QAAQlB,8BAAgB,CAACkB,MAAM;YAC/BC,OAAOnB,8BAAgB,CAACoB,WAAW;YACnCC,UAAUrB,8BAAgB,CAACsB,cAAc;YACzCC,aAAavB,8BAAgB,CAACwB,YAAY;YAC1CC,WAAWzB,8BAAgB,CAAC0B,eAAe;YAC3CC,wBAAwB3B,8BAAgB,CAAC2B,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVC,eAAe9B,8BAAgB,CAAC8B,aAAa;QAC7CnB,SAASX,8BAAgB,CAACW,OAAO;QACjCoB,eAAe/B,8BAAgB,CAAC+B,aAAa;QAC7C7B,QAAQA;QACRgB,QAAQlB,8BAAgB,CAACkB,MAAM;QAC/BD,UAAUjB,8BAAgB,CAACiB,QAAQ;QACnCe,UAAUhC,8BAAgB,CAACgC,QAAQ;QACnCC,kBAAkBjC,8BAAgB,CAACiC,gBAAgB;QACnDC,mBAAmBlC,8BAAgB,CAACkC,iBAAiB;QACrDC,0BAA0BnC,8BAAgB,CAACmC,wBAAwB;QACnEC,YAAYpC,8BAAgB,CAACoC,UAAU;QACvCC,qBAAqBrC,8BAAgB,CAACqC,mBAAmB;QACzDC,eAAetC,8BAAgB,CAACsC,aAAa;QAC7CC,6BAA6BvC,8BAAgB,CAACuC,2BAA2B;QACzEC,qBAAqBxC,8BAAgB,CAACwC,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMnC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAMiC,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjC1C;YACAC,QAAQ0B,qBAAqB1B,MAAM;YACnC0C,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBC,IAAAA,sBAAoB,EAAC9C,KAAK;gBACxB,GAAG2B,oBAAoB;gBACvBoB,oBAAoBxC,YAAYwC,kBAAkB;gBAClD1C;gBACA2C,cAAcC,yBAAc,CAACC,MAAM;gBACnC3C,aAAaA,YAAY2C,MAAM;gBAC/B,GAAGT,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC9C,KAAK;gBACxB,GAAG2B,oBAAoB;gBACvBtB;gBACA0C,oBAAoBxC,YAAYwC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACE,MAAM;gBACnC5C,aAAaA,YAAY4C,MAAM;gBAC/B,GAAGV,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC9C,KAAK;gBACxB,GAAG2B,oBAAoB;gBACvBtB;gBACA0C,oBAAoBxC,YAAYwC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACG,UAAU;gBACvC7C,aAAaA,YAAY6C,UAAU;gBACnCC,kBAAkB;oBAChBC,wBACEvD,8BAAgB,CAACwB,YAAY,CAAEc,aAAa;oBAC9CkB,oCACExD,8BAAgB,CAACwB,YAAY,CAAEiC,wBAAwB;oBACzDC,iCACE1D,8BAAgB,CAACwB,YAAY,CAAEmC,qBAAqB;gBACxD;gBACA,GAAGjB,IAAI;YACT;SACD;IACH;IAEF,MAAMkB,eAAenB,OAAO,CAAC,EAAE;IAC/B,MAAMoB,eAAepB,OAAO,CAAC,EAAE;IAC/B,MAAMqB,aAAarB,OAAO,CAAC,EAAE;IAE7B,IACEmB,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAC,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEAtE,oBAAoBK,QAAQkE,MAAM;IAElCnF,MAAM,CAAC,iBAAiB,CAAC,EAAEO;IAC3B,+EAA+E;IAC/E,MAAMa,eAAeG,YAAY,CAAC;YA8EhC6D;QA7EA,IAAIpE,OAAOqE,YAAY,CAACC,0BAA0B,EAAE;YAClDC,kCAAiB,CAACC,8BAA8B;YAChDD,kCAAiB,CAACE,yBAAyB;QAC7C;QAEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIR;QAEJ,IAAI,CAAC7E,gBAAgBA,iBAAiB,UAAU;YAC9CP,MAAM;YACN,MAAM6F,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcP,gBAAgB,GAAG,MAAMY,IAAAA,qBAAW,EAACrB,cAAc;gBACjEvD;gBACAgE;YACF;YACApF,MAAM,CAAC,yBAAyB,EAAE8F,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACtF,gBAAgBA,iBAAiB,eAAe;YACnDP,MAAM;YACN,MAAM6F,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBR,gBAAgB,GAAGR,aACnC,MAAMoB,IAAAA,qBAAW,EAACpB,YAAY;gBAAExD;gBAAgBgE;YAAgB,KAChE;gBAAC;aAAK;YACVpF,MAAM,CAAC,8BAA8B,EAAE8F,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAcjF,MAAM,CAACsE,MAAM,KAAI,EAACY,oCAAAA,iBAAkBlF,MAAM,CAACsE,MAAM,GAAE;YACpE,MAAMiB,cAAcC,IAAAA,4BAAc;YAClC,IAAK,MAAMC,OAAOF,YAAYG,qBAAqB,CAAE;gBACnD,MAAMC,QAAQJ,YAAYG,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAc5B,aAAa6B,KAAK;gBACtC,IAAIJ,QAAQK,+BAAoB,EAAE;oBAChCF,WAAW,CAACG,+CAAoC,CAAC,GAAG;wBAClDC,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFJ,WAAW,CAACG,+CAAoC,CAAC,CAACC,MAAM;4BAC3DL;yBACD;wBACDM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF,OAAO;oBACLP,WAAW,CAACH,IAAI,GAAG;wBACjBW,UAAU;4BAACL,+CAAoC;yBAAC;wBAChDC,QAAQL;wBACRM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAACtG,gBAAgBA,iBAAiB,UAAU;gBAC9CP,MAAM;gBACN,MAAM6F,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcN,gBAAgB,GAAG,MAAMY,IAAAA,qBAAW,EAACtB,cAAc;oBACjEtD;oBACAgE;gBACF;gBACApF,MAAM,CAAC,yBAAyB,EAAE8F,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEA,IAAI7E,OAAOqE,YAAY,CAACC,0BAA0B,EAAE;YAClDC,kCAAiB,CAACwB,wBAAwB;QAC5C;QACA3B,oCAAAA,yBAAAA,gBAAiB4B,KAAK,qBAAtB5B,4BAAAA;QAEA5E,SAAS;YACPC,UAAU;mBACJiF,CAAAA,gCAAAA,aAAcjF,QAAQ,KAAI,EAAE;mBAC5BkF,CAAAA,gCAAAA,aAAclF,QAAQ,KAAI,EAAE;mBAC5BmF,CAAAA,oCAAAA,iBAAkBnF,QAAQ,KAAI,EAAE;aACrC,CAACwG,MAAM,CAACC,wBAAW;YACpBxG,QAAQ;mBACFgF,CAAAA,gCAAAA,aAAchF,MAAM,KAAI,EAAE;mBAC1BiF,CAAAA,gCAAAA,aAAcjF,MAAM,KAAI,EAAE;mBAC1BkF,CAAAA,oCAAAA,iBAAkBlF,MAAM,KAAI,EAAE;aACnC,CAACuG,MAAM,CAACC,wBAAW;YACpBvG,OAAO;gBACL+E,gCAAAA,aAAc/E,KAAK;gBACnBgF,gCAAAA,aAAchF,KAAK;gBACnBiF,oCAAAA,iBAAkBjF,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNQ,UAAU,CAAC,2BACX8F,OAAO,CAAC,IAAMC,IAAAA,8BAAqB,EAAC5G,QAAQ;IAE/C,MAAM6G,mBAAkB,wBAAA,AAAC3C,aAAuC4C,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3ErH;IAGF,MAAMsH,0BAAyB,wBAAA,AAC7B7C,aACA2C,OAAO,qBAFsB,sBAEpBC,IAAI,CAAClH;IAEhB,MAAMoH,kBAAkBxG,QAAQkE,MAAM,CAACvE;IAEvC,IAAIJ,OAAOE,MAAM,CAACsE,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAIxE,OAAOE,MAAM,CAACsE,MAAM,GAAG,GAAG;YAC5BxE,OAAOE,MAAM,CAACsE,MAAM,GAAG;QACzB;QACA,IAAI0C,QAAQlH,OAAOE,MAAM,CAACuG,MAAM,CAACU,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACI,IAAAA,eAAG,EAAC;QAElB,IACEJ,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACR;YACpC,MAAMS,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC,GADpL,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAN,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,qBAEX,CAFW,IAAID,MACd,oHADU,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,qBAEX,CAFW,IAAID,MACd,CAAC,wBAAwB,EAAEpH,QAAQC,GAAG,CAACsH,WAAW,GAAG,WAAW,UAAU,OAAO,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEZ;QACAF,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,MAAMG,WAAWhB,eAAe,CAAC,EAAE;QACnC,MAAMiB,iBAAiBC,IAAAA,kCAAgB,EAACF;QAExC,IAAIjI,OAAOC,QAAQ,CAACuE,MAAM,GAAG,GAAG;YAC9BC,KAAIC,IAAI,CAAC,CAAC,0BAA0B,EAAEwD,eAAe,EAAE,CAAC;YACxDb,QAAQ3C,IAAI,CAAC1E,OAAOC,QAAQ,CAACwG,MAAM,CAACU,SAASC,IAAI,CAAC;YAClDC,QAAQ3C,IAAI;QACd,OAAO,IAAI,CAAC3E,cAAc;YACxB0E,KAAI2D,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;QACxD;QAEA,OAAO;YACLD,UAAUhB,eAAe,CAAC,EAAE;YAC5BoB,iBAAiB,EAAErB,0CAAAA,uBAAwBqB,iBAAiB;YAC5D5C,aAAaC,IAAAA,4BAAc;YAC3B4C,gBAAgB;gBACdC,QAAQ1B,CAAAA,mCAAAA,gBAAiB0B,MAAM,OAAM,EAAE;gBACvCC,+BACE3B,CAAAA,mCAAAA,gBAAiB2B,6BAA6B,OAAM,EAAE;gBACxDC,iBAAiB5B,CAAAA,mCAAAA,gBAAiB6B,kBAAkB,OAAM,CAAC;YAC7D;QACF;IACF;AACF;AAGO,eAAenJ,WAAWoJ,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAC9BC,SAASH,WAAWI,YAAY,CAACvI,MAAM,CAAEsI,OAAO;IAClD;IACAE,IAAAA,gBAAS,EAAC,aAAaJ;IACvB,0EAA0E;IAC1EK,OAAOC,MAAM,CAAC5I,8BAAgB,EAAEqI,WAAWI,YAAY;IAEvD,0CAA0C;IAC1CI,IAAAA,2BAAoB,EAACR,WAAWS,UAAU;IAE1C,sBAAsB;IACtBC,IAAAA,+BAAiB,EAAC/I,8BAAgB,CAACmF,WAAW;IAE9C,iDAAiD;IACjDnF,8BAAgB,CAACE,MAAM,GAAG,MAAM8I,IAAAA,eAAU,EACxCC,iCAAsB,EACtBjJ,8BAAgB,CAACC,GAAG;IAEtBD,8BAAgB,CAACD,aAAa,GAAGmJ,IAAAA,YAAK,EACpC,CAAC,YAAY,EAAEb,WAAW5I,YAAY,EAAE;IAG1C,MAAMC,SAAS,MAAMV,iBAAiBqJ,WAAW5I,YAAY;IAC7D,MAAM,EAAE0J,YAAY,EAAEC,WAAW,EAAE,GAAG1J,OAAOqI,iBAAiB,IAAI,CAAC;IACnE,IAAIoB,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACf5J,OAAOqI,iBAAiB,CAAEoB,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrB3J,OAAOqI,iBAAiB,CAAEoB,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvD9J,OAAOqI,iBAAiB,CAAEqB,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACAxJ,8BAAgB,CAACD,aAAa,CAAC0J,IAAI;IACnC,OAAO;QAAE,GAAG/J,MAAM;QAAEgK,kBAAkBC,IAAAA,qBAAc;IAAG;AACzD"}