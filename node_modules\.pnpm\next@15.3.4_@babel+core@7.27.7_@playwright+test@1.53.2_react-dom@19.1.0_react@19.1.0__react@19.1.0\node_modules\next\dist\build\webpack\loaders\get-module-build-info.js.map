{"version": 3, "sources": ["../../../../src/build/webpack/loaders/get-module-build-info.ts"], "sourcesContent": ["import type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n  RSCModuleType,\n} from '../../analysis/get-page-static-info'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nexport type ModuleBuildInfo = {\n  nextEdgeMiddleware?: EdgeMiddlewareMeta\n  nextEdgeApiFunction?: EdgeMiddlewareMeta\n  nextEdgeSSR?: EdgeSSRMeta\n  nextWasmMiddlewareBinding?: AssetBinding\n  nextAssetMiddlewareBinding?: AssetBinding\n  usingIndirectEval?: boolean | Set<string>\n  route?: RouteMeta\n  importLocByPath?: Map<string, any>\n  rootDir?: string\n  rsc?: RSCMeta\n}\n\n/**\n * A getter for module build info that casts to the type it should have.\n * We also expose here types to make easier to use it.\n */\nexport function getModuleBuildInfo(webpackModule: webpack.Module) {\n  return webpackModule.buildInfo as ModuleBuildInfo\n}\n\nexport interface RSCMeta {\n  type: RSCModuleType\n  actionIds?: Record<string, string>\n  clientRefs?: string[]\n  clientEntryType?: 'cjs' | 'auto'\n  isClientRef?: boolean\n  requests?: string[] // client requests in flight client entry\n}\n\nexport interface RouteMeta {\n  page: string\n  absolutePagePath: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: MiddlewareConfig\n  // references to other modules that this route needs\n  // e.g. related routes, not-found routes, etc\n  relatedModules?: string[]\n}\n\nexport interface EdgeMiddlewareMeta {\n  page: string\n  matchers?: MiddlewareMatcher[]\n}\n\nexport interface EdgeSSRMeta {\n  isServerComponent: boolean\n  isAppDir?: boolean\n  page: string\n}\n\nexport interface AssetBinding {\n  filePath: string\n  name: string\n}\n"], "names": ["getModuleBuildInfo", "webpackModule", "buildInfo"], "mappings": ";;;;+BAwBgBA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,aAA6B;IAC9D,OAAOA,cAAcC,SAAS;AAChC"}