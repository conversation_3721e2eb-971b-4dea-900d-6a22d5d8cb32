{"version": 3, "sources": ["../../src/cli/next-build.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport '../server/lib/cpu-profile'\nimport { existsSync } from 'fs'\nimport { italic } from '../lib/picocolors'\nimport build from '../build'\nimport { warn } from '../build/output/log'\nimport { printAndExit } from '../server/lib/utils'\nimport isError from '../lib/is-error'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { enableMemoryDebuggingMode } from '../lib/memory/startup'\nimport { disableMemoryDebuggingMode } from '../lib/memory/shutdown'\n\nexport type NextBuildOptions = {\n  debug?: boolean\n  profile?: boolean\n  lint: boolean\n  mangling: boolean\n  turbo?: boolean\n  turbopack?: boolean\n  experimentalDebugMemoryUsage: boolean\n  experimentalAppOnly?: boolean\n  experimentalTurbo?: boolean\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env'\n  experimentalUploadTrace?: string\n}\n\nconst nextBuild = (options: NextBuildOptions, directory?: string) => {\n  process.on('SIGTERM', () => process.exit(143))\n  process.on('SIGINT', () => process.exit(130))\n\n  const {\n    debug,\n    experimentalDebugMemoryUsage,\n    profile,\n    lint,\n    mangling,\n    experimentalAppOnly,\n    experimentalBuildMode,\n    experimentalUploadTrace,\n  } = options\n\n  let traceUploadUrl: string | undefined\n  if (experimentalUploadTrace && !process.env.NEXT_TRACE_UPLOAD_DISABLED) {\n    traceUploadUrl = experimentalUploadTrace\n  }\n\n  if (!lint) {\n    warn('Linting is disabled.')\n  }\n\n  if (!mangling) {\n    warn(\n      'Mangling is disabled. Note: This may affect performance and should only be used for debugging purposes.'\n    )\n  }\n\n  if (profile) {\n    warn(\n      `Profiling is enabled. ${italic('Note: This may affect performance.')}`\n    )\n  }\n\n  if (experimentalDebugMemoryUsage) {\n    process.env.EXPERIMENTAL_DEBUG_MEMORY_USAGE = '1'\n    enableMemoryDebuggingMode()\n  }\n\n  const dir = getProjectDir(directory)\n\n  if (!existsSync(dir)) {\n    printAndExit(`> No such directory exists as the project root: ${dir}`)\n  }\n\n  const isTurbopack = Boolean(\n    options.turbo || options.turbopack || process.env.IS_TURBOPACK_TEST\n  )\n  if (isTurbopack) {\n    process.env.TURBOPACK = '1'\n  }\n\n  return build(\n    dir,\n    profile,\n    debug || Boolean(process.env.NEXT_DEBUG_BUILD),\n    lint,\n    !mangling,\n    experimentalAppOnly,\n    isTurbopack,\n    experimentalBuildMode,\n    traceUploadUrl\n  )\n    .catch((err) => {\n      if (experimentalDebugMemoryUsage) {\n        disableMemoryDebuggingMode()\n      }\n      console.error('')\n      if (\n        isError(err) &&\n        (err.code === 'INVALID_RESOLVE_ALIAS' ||\n          err.code === 'WEBPACK_ERRORS' ||\n          err.code === 'BUILD_OPTIMIZATION_FAILED' ||\n          err.code === 'NEXT_EXPORT_ERROR' ||\n          err.code === 'NEXT_STATIC_GEN_BAILOUT' ||\n          err.code === 'EDGE_RUNTIME_UNSUPPORTED_API')\n      ) {\n        printAndExit(`> ${err.message}`)\n      } else {\n        console.error('> Build error occurred')\n        printAndExit(err)\n      }\n    })\n    .finally(() => {\n      if (experimentalDebugMemoryUsage) {\n        disableMemoryDebuggingMode()\n      }\n    })\n}\n\nexport { nextBuild }\n"], "names": ["nextBuild", "options", "directory", "process", "on", "exit", "debug", "experimentalDebugMemoryUsage", "profile", "lint", "mangling", "experimentalAppOnly", "experimentalBuildMode", "experimentalUploadTrace", "traceUploadUrl", "env", "NEXT_TRACE_UPLOAD_DISABLED", "warn", "italic", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "enableMemoryDebuggingMode", "dir", "getProjectDir", "existsSync", "printAndExit", "isTurbopack", "Boolean", "turbo", "turbopack", "IS_TURBOPACK_TEST", "TURBOPACK", "build", "NEXT_DEBUG_BUILD", "catch", "err", "disableMemoryDebuggingMode", "console", "error", "isError", "code", "message", "finally"], "mappings": ";;;;;+BAuHS<PERSON>;;;eAAAA;;;QArHF;oBACoB;4BACJ;8DACL;qBACG;uBACQ;gEACT;+BACU;yBACY;0BACC;;;;;;AAgB3C,MAAMA,YAAY,CAACC,SAA2BC;IAC5CC,QAAQC,EAAE,CAAC,WAAW,IAAMD,QAAQE,IAAI,CAAC;IACzCF,QAAQC,EAAE,CAAC,UAAU,IAAMD,QAAQE,IAAI,CAAC;IAExC,MAAM,EACJC,KAAK,EACLC,4BAA4B,EAC5BC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,mBAAmB,EACnBC,qBAAqB,EACrBC,uBAAuB,EACxB,GAAGZ;IAEJ,IAAIa;IACJ,IAAID,2BAA2B,CAACV,QAAQY,GAAG,CAACC,0BAA0B,EAAE;QACtEF,iBAAiBD;IACnB;IAEA,IAAI,CAACJ,MAAM;QACTQ,IAAAA,SAAI,EAAC;IACP;IAEA,IAAI,CAACP,UAAU;QACbO,IAAAA,SAAI,EACF;IAEJ;IAEA,IAAIT,SAAS;QACXS,IAAAA,SAAI,EACF,CAAC,sBAAsB,EAAEC,IAAAA,kBAAM,EAAC,uCAAuC;IAE3E;IAEA,IAAIX,8BAA8B;QAChCJ,QAAQY,GAAG,CAACI,+BAA+B,GAAG;QAC9CC,IAAAA,kCAAyB;IAC3B;IAEA,MAAMC,MAAMC,IAAAA,4BAAa,EAACpB;IAE1B,IAAI,CAACqB,IAAAA,cAAU,EAACF,MAAM;QACpBG,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEH,KAAK;IACvE;IAEA,MAAMI,cAAcC,QAClBzB,QAAQ0B,KAAK,IAAI1B,QAAQ2B,SAAS,IAAIzB,QAAQY,GAAG,CAACc,iBAAiB;IAErE,IAAIJ,aAAa;QACftB,QAAQY,GAAG,CAACe,SAAS,GAAG;IAC1B;IAEA,OAAOC,IAAAA,cAAK,EACVV,KACAb,SACAF,SAASoB,QAAQvB,QAAQY,GAAG,CAACiB,gBAAgB,GAC7CvB,MACA,CAACC,UACDC,qBACAc,aACAb,uBACAE,gBAECmB,KAAK,CAAC,CAACC;QACN,IAAI3B,8BAA8B;YAChC4B,IAAAA,oCAA0B;QAC5B;QACAC,QAAQC,KAAK,CAAC;QACd,IACEC,IAAAA,gBAAO,EAACJ,QACPA,CAAAA,IAAIK,IAAI,KAAK,2BACZL,IAAIK,IAAI,KAAK,oBACbL,IAAIK,IAAI,KAAK,+BACbL,IAAIK,IAAI,KAAK,uBACbL,IAAIK,IAAI,KAAK,6BACbL,IAAIK,IAAI,KAAK,8BAA6B,GAC5C;YACAf,IAAAA,mBAAY,EAAC,CAAC,EAAE,EAAEU,IAAIM,OAAO,EAAE;QACjC,OAAO;YACLJ,QAAQC,KAAK,CAAC;YACdb,IAAAA,mBAAY,EAACU;QACf;IACF,GACCO,OAAO,CAAC;QACP,IAAIlC,8BAA8B;YAChC4B,IAAAA,oCAA0B;QAC5B;IACF;AACJ"}