/// <reference types="node"/>

/**
Show cursor.

@param stream - Default: `process.stderr`.

@example
```
import * as cliCursor from 'cli-cursor';

cliCursor.show();
```
*/
export function show(stream?: NodeJS.WritableStream): void;

/**
Hide cursor.

@param stream - Default: `process.stderr`.

@example
```
import * as cliCursor from 'cli-cursor';

cliCursor.hide();
```
*/
export function hide(stream?: NodeJS.WritableStream): void;

/**
Toggle cursor visibility.

@param force - Is useful to show or hide the cursor based on a boolean.
@param stream - Default: `process.stderr`.

@example
```
import * as cliCursor from 'cli-cursor';

const unicornsAreAwesome = true;
cliCursor.toggle(unicornsAreAwesome);
```
*/
export function toggle(force?: boolean, stream?: NodeJS.WritableStream): void;
